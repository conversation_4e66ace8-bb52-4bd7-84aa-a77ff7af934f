# Alec <PERSON>cheli
# Financial modeling rules for aircraft leasing ROI analysis

###################################
# Setup cost data from Excel
###################################

# Rule to process Excel file and create TSV files for each sheet
rule setup_cost_data:
    input:
        excel_file = DATA_DIR + "/estimated_costs_master_document.xlsx",
        script = BIN_DIR + "/scripts/001a-process_excel_to_tsv.py"

    output:
        aircraft_budget = DATA_DIR + "/aircraft_costs/aircraft_budget_projections.tsv",
        corporate_costs = DATA_DIR + "/corporate_costs/corporate_costs_projections.tsv",
        operating_costs = DATA_DIR + "/operating_costs/operating_costs_projections.tsv"

    shell:
        "{PYTHON} {input.script} --input_excel {input.excel_file} --output_dir {DATA_DIR}"


# Rule to calculate maximum aircraft purchase budget
rule calculate_aircraft_budget:
    input:
        aircraft_costs = DATA_DIR + "/aircraft_costs/aircraft_budget_projections.tsv",
        corporate_costs = DATA_DIR + "/corporate_costs/corporate_costs_projections.tsv",
        script = BIN_DIR + "/scripts/001b-calculate_aircraft_budget.py"

    output:
        budget_data = RES_DIR + "/processed_data/aircraft_budget_analysis.tsv"

    shell:
        "{PYTHON} {input.script} --aircraft_costs {input.aircraft_costs} --corporate_costs {input.corporate_costs} --output_file {output.budget_data}"


# Rule to visualize aircraft budget analysis
rule visualize_aircraft_budget:
    input:
        budget_data = RES_DIR + "/processed_data/aircraft_budget_analysis.tsv",
        script = BIN_DIR + "/scripts/001c-visualize_aircraft_budget.R"

    output:
        budget_figure = RES_DIR + "/figures/aircraft_budget_analysis.pdf"

    shell:
        "{RSCRIPT} {input.script} -a {input.budget_data} -b {output.budget_figure}"


