I want to generate snakemake rules in /Users/<USER>/Library/CloudStorage/Dropbox/leasing_financing_private_aircraft/NewCO/1._<PERSON>_Workspace/financial_modeling/bin/snakemake/003-advanced_financing_model.smk and scripts to do the following:



Let's create a new rule in (/Users/<USER>/Library/CloudStorage/Dropbox/leasing_financing_private_aircraft/NewCO/1._Alec_Workspace/financial_modeling/bin/snakemake/005-pitch_deck_visuals.smk) to do the following:



1. Let's create a python script called imbd.py to do the following:
- Using Python,read /Users/<USER>/Library/CloudStorage/Dropbox/_long-term_storage/friends_family/olivia_gelmon/data/IMDB.csv and print the first six rows of the dataset
- Using Python, print the minimum, maximum, mean, median, and standard deviation of the gross box office revenues.
- Using Python, create a histogram of the gross box office revenues (found in the gross column of the dataset). Make sure to use 15 bins for your plot.
- How many movies had a gross box office of over $100,000,000? Write down the names of these movies.

2. Let's create a python script called happy.py to do the following:
- Using Python, draw side-by-side modified boxplots comparing the distribution of the Life Ladder score (Life. Ladder) for the three countries.
- Using Python, provide numerical summaries for the distribution of the Life Ladder scores (Life. Ladder) across the three countries.
- Do the boxplots indicate the presence of outliers in any of the three distributions?
If so, write down which distribution and when. If not, write "NONE".


For all steps of this thread, make sure you follow these rules:

1. Use python to perform data analysis and table generation. When a command line tool would work better, then use it. But, use command line tools in their own job and do not use a python wrapper for these tools. For figure generation, use R scripts. 

2. Use /Users/<USER>/opt/miniconda3/envs/py_dev/bin/python within the py_dev environment for testing python locally. 

3. Use /Users/<USER>/opt/miniconda3/envs/r4_env/bin/Rscript for the Rscript when testing locally for figure generation and /Users/<USER>/opt/miniconda3/envs/testing/bin/Rscript as "AP_SCRIPT" but keep the original Snakemake Rscripts paths. 

4. For all R scripts, use "=" and not "<-" for all cases. 

5. All snakemake rules should follow the same syntax as /Users/<USER>/Library/CloudStorage/Dropbox/leasing_financing_private_aircraft/NewCO/1._Alec_Workspace/financial_modeling/bin/reference_scripts/145-LR_SR_ecdna_comparison.smk. All python scripts should follow the same syntax as /Users/<USER>/Library/CloudStorage/Dropbox/leasing_financing_private_aircraft/NewCO/1._Alec_Workspace/financial_modeling/bin/reference_scripts/002a-combine_decoil_results.py. All R script should follow the same syntax as /Users/<USER>/Library/CloudStorage/Dropbox/leasing_financing_private_aircraft/NewCO/1._Alec_Workspace/financial_modeling/bin/reference_scripts/002b-visualize_decoil_results.R. 

6. Do not print progress updated in the Python or R scripts. The syntax is very important.

7. Unless explicitly stated otherwise, put results in the directory: '/Users/<USER>/Library/CloudStorage/Dropbox/_long-term_storage/friends_family/olivia_gelmon/results' and the subdirs "figures" and "processed_data" (figures only has figure files, processed_data has everything else). 

8. Unless explicitly stated otherwise, all R scripts should be for figure generation only, not table generation or printing commands (use python scripts for this).

9. Snakemake is within the conda env /Users/<USER>/opt/miniconda3/envs/snakemake

10. When reading values from excel sheets, if a cell is a formula, you must calculate the value by following the formula, not by reading the value of the cell itself.

11. Read data from the data dir: /Users/<USER>/Library/CloudStorage/Dropbox/_long-term_storage/friends_family/olivia_gelmon/data
