# Alec Ba<PERSON>chel<PERSON>
# comparing ecDNA detection methods (long and short reads)

###################################
# ecDNA long and short read comparison analysis
###################################

# Combine method counts for comparison (long and short reads)
rule combine_ecdna_counts_comparison:
    input:
        amplicon_counts = RES_DIR + "/circ_dna/combined_amplicon_results_counts.tsv",
        decoil_counts = RES_DIR + "/analysis_nanopore/ecdna/decoil/ecdna_counts.tsv",

        script = BIN_DIR + "/circ_dna_scripts/004a-combine_method_counts.py"

    output:
        combined_counts = RES_DIR + "/analysis_nanopore/ecdna/method_comparison/ecdna_counts_combined.tsv"

    shell:
        "{PYTHON} {input.script} --amplicon_counts {input.amplicon_counts} --decoil_counts {input.decoil_counts} --output_file {output.combined_counts}"

# Visualize method comparison counts (long and short reads)
rule visualize_ecdna_counts_comparison:
    input:
        combined_counts = RES_DIR + "/analysis_nanopore/ecdna/method_comparison/ecdna_counts_combined.tsv",

        script = BIN_DIR + "/circ_dna_scripts/004b-visualize_ecdna_counts_comparison.R"

    output:
        comparison_figure = RES_DIR + "/analysis_nanopore/ecdna/_figures/method_comparison_counts.pdf"

    shell:
        "{RSCRIPT} {input.script} --combined_counts {input.combined_counts} --figure_file {output.comparison_figure}"



# Combine oncogene counts for comparison (long and short reads)
rule combine_oncogene_analysis_ecdna:
    input:
        amplicon_results = RES_DIR + "/circ_dna/combined_oncogene_enhancer_results.tsv",
        decoil_oncogene_analysis = RES_DIR + "/analysis_nanopore/ecdna/gene_intersections/ecdna_oncogene_analysis.tsv",
        script = BIN_DIR + "/circ_dna_scripts/004c-combine_oncogene_analysis_ecdna.py"

    output:
        combined_oncogene_counts = RES_DIR + "/analysis_nanopore/ecdna/method_comparison/combined_oncogene_counts.tsv"

    shell:
        "{PYTHON} {input.script} --amplicon_results {input.amplicon_results} --decoil_oncogene_analysis {input.decoil_oncogene_analysis} --output_file {output.combined_oncogene_counts}"

# Visualize method comparison oncogene content (long and short reads)
rule visualize_oncogene_counts:
    input:
        combined_oncogene_counts = RES_DIR + "/analysis_nanopore/ecdna/method_comparison/combined_oncogene_counts.tsv",

        script = BIN_DIR + "/circ_dna_scripts/004d-visualize_oncogene_counts.R"

    output:
        comparison_figure = RES_DIR + "/analysis_nanopore/ecdna/_figures/method_comparison_oncogenes.pdf"

    shell:
        "{RSCRIPT} {input.script} --combined_oncogene_counts {input.combined_oncogene_counts} --figure_file {output.comparison_figure}"


# Calculate cohort fraction with ecDNAs for comparison (long and short reads)
rule calculate_cohort_fraction_ecdna:
    input:
        combined_counts = RES_DIR + "/analysis_nanopore/ecdna/method_comparison/ecdna_counts_combined.tsv",
        script = BIN_DIR + "/circ_dna_scripts/004e-calculate_cohort_fraction.py"

    output:
        cohort_fraction = RES_DIR + "/analysis_nanopore/ecdna/method_comparison/cohort_fraction_with_ecdna.tsv"

    shell:
        "{PYTHON} {input.script} --combined_counts {input.combined_counts} --output_file {output.cohort_fraction}"


# Visualize cohort fraction comparison (long and short reads)
rule visualize_cohort_fraction_ecdna:
    input:
        cohort_fraction = RES_DIR + "/analysis_nanopore/ecdna/method_comparison/cohort_fraction_with_ecdna.tsv",
        script = BIN_DIR + "/circ_dna_scripts/004f-visualize_cohort_fraction.R"

    output:
        fraction_figure = RES_DIR + "/analysis_nanopore/ecdna/_figures/cohort_fraction_comparison.pdf"

    shell:
        "{RSCRIPT} {input.script} --cohort_fraction {input.cohort_fraction} --figure_file {output.fraction_figure}"



