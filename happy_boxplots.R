# <PERSON>
library(optparse)
library(ggplot2)
library(dplyr)

# options list for parser options
option_list <- list(
    make_option(c("-i","--input_file"), type="character", default=NULL,
            help="Happy CSV file",
            dest="input_file"),
    make_option(c("-o","--output_file"), type="character", default=NULL,
            help="Output PDF file for boxplots",
            dest="output_file")
)

parser <- OptionParser(usage = "%prog -i input.csv -o output.pdf", option_list=option_list)

# get the parse information and assign to groups in the opt variable
opt = parse_args(parser)

plot_theme = function(...) {
    theme_bw() +
    theme(    
        plot.title = element_text(size = 22),
        plot.caption = element_text(size = 12),
        plot.subtitle = element_text(size = 16),
        axis.title = element_text(size = 18),
        axis.text.x = element_text(size = 12, color = "black"),
        axis.text.y = element_text(size = 12, color = "black"),
        legend.title = element_text(size = 16),
        legend.text = element_text(size = 14),
        ...
    )
}

create_boxplots = function(df) {
    
    # Filter for the three countries
    countries = c("Canada", "Japan", "Sweden")
    filtered_df = df[df$Country.name %in% countries, ]
    
    # Remove rows with missing Life.Ladder values
    filtered_df = filtered_df[!is.na(filtered_df$Life.Ladder), ]
    
    # Create the boxplot
    p <- ggplot(filtered_df, aes(x = Country.name, y = Life.Ladder, fill = Country.name)) +
        geom_boxplot(alpha = 0.7, outlier.shape = 16, outlier.size = 2) +
        scale_fill_manual(values = c("Canada" = "#E31A1C", "Japan" = "#1F78B4", "Sweden" = "#33A02C")) +
        labs(
            title = "Distribution of Life Ladder Scores by Country",
            subtitle = "Side-by-side Modified Boxplots",
            x = "Country",
            y = "Life Ladder Score",
            fill = "Country"
        ) +
        plot_theme() +
        theme(
            axis.text.x = element_text(angle = 0, hjust = 0.5),
            legend.position = "none"
        )
    
    return(p)
}

# Open PDF device
pdf(opt$output_file, width = 10, height = 8)

# Load data
df <- read.csv(opt$input_file)

# Create and print the boxplot
boxplot = create_boxplots(df)
print(boxplot)

# Close PDF device
dev.off()

cat("Boxplot saved to:", opt$output_file, "\n")
