# Alec Ba<PERSON>cheli

# run version (typically date)
MAIN_DIR='/Users/<USER>/Library/CloudStorage/Dropbox/leasing_financing_private_aircraft/NewCO/1._Alec_Workspace/financial_modeling'

# directory of scripts / binaries
BIN_DIR = "/".join([MAIN_DIR, "bin"])

# data and results directories
DATA_DIR = "/".join([MAIN_DIR, "data"])
RES_DIR = "/".join([MAIN_DIR, "results"])


# location of R environment for running R scripts 
RSCRIPT='/Users/<USER>/opt/miniconda3/envs/r4_env/bin/Rscript'
# location of python
PYTHON='/Users/<USER>/opt/miniconda3/envs/py_dev/bin/python'



###################################
# Complete workflow
###################################

rule all:
       input:
              RES_DIR + "/processed_data/aircraft_budget_analysis.tsv",
              RES_DIR + "/figures/aircraft_budget_analysis.pdf",
              RES_DIR + "/figures/stacked_cost_plot.pdf",
              RES_DIR + "/figures/separate_cost_plot.pdf",


              expand(RES_DIR + "/figures/advanced_financing_line_plots_{period_months}_months_no_tax.pdf", period_months=[120, 42]),
              expand(RES_DIR + "/figures/advanced_financing_bar_plots_{period_months}_months_no_tax.pdf", period_months=[120, 42]),

              RES_DIR + "/processed_data/loan_amortization_schedule_120_months.xlsx",
              RES_DIR + "/processed_data/month_0_budget.xlsx",
              RES_DIR + "/processed_data/monthly_income_statements_42_months.xlsx",

              # Pitch deck visuals
              RES_DIR + "/figures/aircraft_appreciation_comparison.pdf",
              RES_DIR + "/figures/aircraft_loan_comparison.pdf",
              RES_DIR + "/figures/loan_payments_cumulative.pdf"
              
        # "/Users/<USER>/Desktop/aircraft_tmp/loan_investment_analysis.pdf",
        # "/Users/<USER>/Desktop/aircraft_tmp/full_financing_roi.pdf"


#####################
# sequencing and clinical overview 
#####################

include: "snakemake/001-financial_modeling.smk"
include: "snakemake/002-purchasing_and_acquisition.smk"
include: "snakemake/003-advanced_financing_model.smk"
include: "snakemake/004-fill_projections_results.smk"
include: "snakemake/005-pitch_deck_visuals.smk"
