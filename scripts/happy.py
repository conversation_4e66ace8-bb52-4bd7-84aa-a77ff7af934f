# I will use pandas to import data frames and matplotlib to create boxplots
import pandas as pd
import matplotlib.pyplot as plt

# declare the input files
happy_data_file = '/Users/<USER>/Library/CloudStorage/Dropbox/_long-term_storage/friends_family/olivia_gelmon/data/Happy.csv'
# HAPPY_DATA_FILE = 'Happy.csv'
figure_file = '/Users/<USER>/Library/CloudStorage/Dropbox/_long-term_storage/friends_family/olivia_gelmon/results/figures/happy_boxplot.png'
# figure_file = 'happy_boxplot.png'


# 1)
# load the data
df = pd.read_csv(happy_data_file)

# draw side-by-side modified boxplots comparing the distribution of the Life Ladder score (Life. Ladder) for the three countries


