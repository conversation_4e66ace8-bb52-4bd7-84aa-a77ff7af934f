#!/usr/bin/env python3
# <PERSON>cheli
# Process acquisition costs data for pitch deck visualization

import argparse
import pandas as pd
import numpy as np

def parse_arguments():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(description="Process acquisition costs data for pitch deck visualization")
    parser.add_argument("--input_file", required=True, help="Input acquisition costs TSV file")
    parser.add_argument("--output_file", required=True, help="Output processed costs TSV file")
    return parser.parse_args()

def process_acquisition_costs(input_file, output_file):
    """
    Process acquisition costs data to compare purchasing vs operating income costs.
    Separates costs into parts and labor, and categorizes as purchasing or operating income.
    """
    
    # Load the acquisition costs data
    df = pd.read_csv(input_file, sep='\t')
    
    
    # Fill NaN values with 0 for parts and labor columns
    df['parts'] = df['parts'].fillna(0)
    df['labor'] = df['labor'].fillna(0)
    
    # Create two main categories: Purchasing and Operating income
    df['main_category'] = df['category'].apply(
        lambda x: 'Operating income' if x == 'Rental income' else 'Purchasing'
    )
    
    # Group by main category and sum parts and labor costs
    summary = df.groupby('main_category').agg({
        'parts': 'sum',
        'labor': 'sum',
        'total_cost': 'sum'
    }).reset_index()
    
    # Calculate total costs for verification
    summary['calculated_total'] = summary['parts'] + summary['labor']
    

    # Create the final output format for visualization
    # We need to reshape the data for stacked bar plots
    output_data = []
    
    for _, row in summary.iterrows():
        # Add parts cost row
        output_data.append({
            'category': row['main_category'],
            'cost_type': 'Parts',
            'cost': row['parts']
        })
        
        # Add labor cost row
        output_data.append({
            'category': row['main_category'],
            'cost_type': 'Labor', 
            'cost': row['labor']
        })
    
    # Convert to DataFrame
    output_df = pd.DataFrame(output_data)
    
    # Save the processed data
    output_df.to_csv(output_file, sep='\t', index=False)


def main():
    """Main function"""
    args = parse_arguments()
    process_acquisition_costs(args.input_file, args.output_file)

if __name__ == "__main__":
    main()
