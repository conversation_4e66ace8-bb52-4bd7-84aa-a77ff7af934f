# <PERSON>
library(optparse)
library(ggplot2)
library(dplyr)
library(scales)

# options list for parser options
option_list = list(
    make_option(c("-a","--input_file"), type="character", default=NULL,
            help="Input processed costs TSV file",
            dest="input_file"),
    make_option(c("-b","--output_file"), type="character", default=NULL,
            help="Output PDF file",
            dest="output_file")
)

parser = OptionParser(usage = "%prog -a input.tsv -b output.pdf", option_list=option_list)

# get the parse information and assign to groups in the opt variable
opt = parse_args(parser)

plot_theme = function(...) {
    theme_bw() +
    theme(    
        plot.title = element_text(size = 22),
        plot.caption = element_text(size = 12),
        plot.subtitle = element_text(size = 16),
        axis.title = element_text(size = 18),
        axis.text.x = element_text(size = 12, color = "black"),
        axis.text.y = element_text(size = 12, color = "black"),
        legend.title = element_text(size = 16),
        legend.text = element_text(size = 14),
        ...
    )
}

create_acquisition_plot = function(data) {

    # Create the stacked bar plot
    p = ggplot(data, aes(x = category, y = cost, fill = cost_type)) +
        geom_bar(stat = "identity", position = "stack", width = 0.6) +

        # Customize colors for parts and labor
        scale_fill_manual(
            values = c("Parts" = "#2E86AB", "Labor" = "#A23B72"),
            name = "Cost Type"
        ) +

        # Format y-axis with proper currency formatting
        scale_y_continuous(
            labels = function(x) paste0("$", format(x, big.mark = ",", scientific = FALSE)),
            expand = expansion(mult = c(0, 0.1))
        ) +

        # Remove x-axis label as requested
        labs(
            x = "",
            y = "Total cost (CAD)",
            title = "Aircraft Acquisition Costs: Purchasing vs Operating Income",
            subtitle = "Breakdown by Parts and Labor Components"
        ) +

        plot_theme()

    # Add value labels on top of bars
    p = p +
        geom_text(
            data = data %>%
                group_by(category) %>%
                summarise(total = sum(cost), .groups = "drop"),
            aes(x = category, y = total, label = paste0("$", format(total, big.mark = ","))),
            inherit.aes = FALSE,
            vjust = -0.5,
            size = 4,
            fontface = "bold"
        )

    return(p)
}

pdf(opt$output_file)

# Load data
data = read.delim(opt$input_file, sep = "\t", stringsAsFactors = FALSE)

# Create plot
p = create_acquisition_plot(data)
print(p)

dev.off()
