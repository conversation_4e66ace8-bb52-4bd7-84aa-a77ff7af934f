# Data Analysis Scripts for IMDB and Happiness Datasets

This repository contains Python and R scripts for analyzing IMDB movie data and World Happiness Report data.

## Scripts Overview

### 1. imbd.py - IMDB Movie Analysis

**Purpose**: Analyzes IMDB movie dataset to examine box office performance

**Features**:
- Displays first 6 rows of the dataset
- Calculates statistical summaries for gross box office revenues (min, max, mean, median, std)
- Creates a histogram of gross revenues with 15 bins
- Identifies movies with gross box office over $100,000,000

**Usage**:
```bash
/Users/<USER>/opt/miniconda3/envs/py_dev/bin/python imbd.py \
  --input_file /Users/<USER>/Library/CloudStorage/Dropbox/_long-term_storage/friends_family/olivia_gelmon/data/IMDB.csv \
  --output_dir /Users/<USER>/Library/CloudStorage/Dropbox/_long-term_storage/friends_family/olivia_gelmon/results
```

**Outputs**:
- Console output with dataset preview and statistics
- `figures/gross_revenue_histogram.png` - Histogram of box office revenues
- `processed_data/high_grossing_movies.txt` - List of high-grossing movies

### 2. happy.py - Happiness Data Analysis

**Purpose**: Analyzes World Happiness Report data for Canada, Japan, and Sweden

**Features**:
- Filters data for three countries (Canada, Japan, Sweden)
- Calculates numerical summaries for Life Ladder scores by country
- Detects outliers using IQR method
- Provides comparative analysis between Canada and Japan

**Usage**:
```bash
/Users/<USER>/opt/miniconda3/envs/py_dev/bin/python happy.py \
  --input_file /Users/<USER>/Library/CloudStorage/Dropbox/_long-term_storage/friends_family/olivia_gelmon/data/Happy.csv \
  --output_dir /Users/<USER>/Library/CloudStorage/Dropbox/_long-term_storage/friends_family/olivia_gelmon/results
```

**Outputs**:
- Console output with statistical summaries and analysis
- `processed_data/life_ladder_summary.csv` - Numerical summaries by country
- `processed_data/outlier_analysis.txt` - Outlier detection results
- `processed_data/country_comparison_analysis.txt` - Comparative analysis

### 3. happy_boxplots.R - Boxplot Visualization

**Purpose**: Creates side-by-side modified boxplots for Life Ladder scores

**Features**:
- Generates boxplots comparing Life Ladder distributions across three countries
- Uses modified boxplots with outlier detection
- Professional styling with custom colors

**Usage**:
```bash
/Users/<USER>/opt/miniconda3/envs/r4_env/bin/Rscript happy_boxplots.R \
  -i /Users/<USER>/Library/CloudStorage/Dropbox/_long-term_storage/friends_family/olivia_gelmon/data/Happy.csv \
  -o /Users/<USER>/Library/CloudStorage/Dropbox/_long-term_storage/friends_family/olivia_gelmon/results/figures/life_ladder_boxplots.pdf
```

**Outputs**:
- `figures/life_ladder_boxplots.pdf` - Side-by-side boxplots

## Key Findings

### IMDB Analysis Results:
- **Dataset size**: 50 movies
- **Gross revenue statistics**:
  - Minimum: $386,078
  - Maximum: $173,046,663
  - Mean: $29,600,208
  - Median: $18,074,865
  - Standard Deviation: $40,553,012
- **High-grossing movies** (>$100M): 3 movies
  - Scream
  - Scream 2
  - Scream 3

### Happiness Analysis Results:
- **Outlier detection**: Japan has 1 outlier (Life Ladder score: 6.516)
- **Country comparison**:
  - Canada: Mean = 7.297, Std = 0.242
  - Japan: Mean = 6.024, Std = 0.182
  - Sweden: Mean = 7.365, Std = 0.106
- **Key insights**: Canada shows higher life satisfaction than Japan with greater variability, suggesting more diverse well-being experiences

## Directory Structure

```
results/
├── figures/
│   ├── gross_revenue_histogram.png
│   └── life_ladder_boxplots.pdf
└── processed_data/
    ├── high_grossing_movies.txt
    ├── life_ladder_summary.csv
    ├── outlier_analysis.txt
    └── country_comparison_analysis.txt
```

## Dependencies

- **Python**: pandas, numpy, matplotlib, argparse
- **R**: optparse, ggplot2, dplyr

## Environment Setup

- Python scripts use: `/Users/<USER>/opt/miniconda3/envs/py_dev/bin/python`
- R scripts use: `/Users/<USER>/opt/miniconda3/envs/r4_env/bin/Rscript`
